'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import { DateFormat } from '@/helper/common/commonFunctions';
import NoDataView from '@/components/UI/NoDataView';
import './recenttickets.scss';

const RecentTickets = ({
  tickets = [],
  title = 'Recent Tickets',
  emptyMessage = 'No recent tickets found',
  showPriority = true,
  className = 'recent-tickets',
}) => {
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'draft',
      escalated: 'failed',
      in_progress: 'ongoing',
      on_hold: 'ongoing',
      qa_review: 'probation',
      assigned: 'probation',
      under_review: 'probation',
      resolved: 'success',
      closed: 'status-verified',
    };
    return statusMap[status] || 'success';
  };

  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed', // Red - Most critical
      high: 'high', // Orange/Yellow - High priority but less than urgent
      medium: 'ongoing', // Blue - Medium priority
      low: 'draft', // Green - Low priority
      none: 'draft', // Blue - Default
    };
    return priorityMap[priority] || 'success';
  };

  const formatStatus = (status) => {
    // Convert API status format to display format
    const statusMap = {
      in_progress: 'In Progress',
      open: 'Open',
      resolved: 'Resolved',
      closed: 'Closed',
      escalated: 'Escalated',
      qa_review: 'QA Review',
      assigned: 'Assigned',
      under_review: 'Under Review',
      on_hold: 'On Hold',
    };
    return statusMap[status] || status;
  };

  const formatPriority = (priority) => {
    // Convert API priority format to display format
    const priorityMap = {
      high: 'High',
      medium: 'Medium',
      low: 'Low',
      urgent: 'Urgent',
    };
    return priorityMap[priority] || priority;
  };

  return (
    <Box className={className}>
      <Box className={`${className}__header`}>
        <Typography className="sub-header-text">{title}</Typography>
      </Box>

      <Box className={`${className}__content`}>
        {tickets.length > 0 ? (
          <Box className={`${className}__list`}>
            {tickets.map((ticket) => (
              <Box key={ticket.id} className={`${className}__item`}>
                <Box className={`${className}__item-header`}>
                  <Typography className="title-text">{ticket.title}</Typography>
                  <Typography className="sub-title-text">
                    {DateFormat(ticket.date, 'datesWithhour')}
                  </Typography>
                </Box>

                <Box className={`${className}__item-footer`}>
                  {showPriority ? (
                    <Box className={`${className}__item-badges`}>
                      <Typography
                        className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                      >
                        {formatStatus(ticket.status)}
                      </Typography>
                      <Typography
                        className={`sub-title-text fw600 ${getPriorityClass(ticket.priority)}`}
                      >
                        {formatPriority(ticket.priority)}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography
                      className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                    >
                      {formatStatus(ticket.status)}
                    </Typography>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Box className={`${className}__empty`}>
            <NoDataView
              title={emptyMessage}
              description="There is no tickets available at the moment."
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default RecentTickets;
